# Simple DNS query test script
$udpClient = New-Object System.Net.Sockets.UdpClient
$serverEndpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Parse('127.0.0.1'), 2053)

# Create a simple DNS query for 'google.com' A record
# DNS Header (12 bytes): ID=0x1234, Flags=0x0100 (standard query), QDCOUNT=1, others=0
$query = [byte[]](0x12, 0x34, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00)

# Question section: 'codecrafters.io' A record, class IN
# 12 'codecrafters' 2 'io' 0 (null terminator) 0x0001 (A) 0x0001 (IN)
$question = [byte[]](0x0C) + [System.Text.Encoding]::ASCII.GetBytes('codecrafters') + [byte[]](0x02) + [System.Text.Encoding]::ASCII.GetBytes('io') + [byte[]](0x00, 0x00, 0x01, 0x00, 0x01)

$fullQuery = $query + $question
Write-Host "Sending DNS query for codecrafters.io..."
$udpClient.Send($fullQuery, $fullQuery.Length, $serverEndpoint)

# Receive response
$response = $udpClient.Receive([ref]$serverEndpoint)
Write-Host "Response received: $($response.Length) bytes"
$udpClient.Close()
